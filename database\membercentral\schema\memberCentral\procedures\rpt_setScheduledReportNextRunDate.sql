ALTER PROC dbo.rpt_setScheduledReportNextRunDate
@itemID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @existingNextRunDate datetime, @interval int, @sqldatepart varchar(2), @nextRunDate datetime, @endRunDate datetime;
	
	SELECT @existingNextRunDate = sr.nextRunDate, @endRunDate = sr.endRunDate, @interval = sr.interval, @sqldatepart = stit.sqldatepart
	FROM dbo.rpt_scheduledReports as sr
	INNER JOIN dbo.scheduledTaskIntervalTypes as stit on stit.intervalTypeID = sr.intervalTypeID 
	WHERE sr.itemID = @itemID;

	EXEC dbo.sched_getNextRunDate @existingNextRunDate=@existingNextRunDate, @interval=@interval, 
		@sqldatepart=@sqldatepart, @nextRunDate=@nextRunDate OUTPUT;
	
	IF (@endRunDate IS NOT NULL AND @nextRunDate > @endRunDate)
		SET @nextRunDate = null;

	UPDATE dbo.rpt_scheduledReports
	SET nextRunDate = @nextRunDate,
		dateLastUpdated = GETDATE()
	WHERE itemID = @itemID;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
