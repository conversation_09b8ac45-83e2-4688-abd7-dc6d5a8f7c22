ALTER PROC dbo.rpt_markScheduledReportAsFinished
@itemID int,
@isSuccess bit

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	BEGIN TRAN;
		UPDATE dbo.rpt_scheduledReports
		SET isRunning = 0,
			dateLastUpdated = GETDATE()
		WHERE itemID = @itemID;

		-- if success, set the next run date based on interval settings.
		-- if failure, advance next run date 10 mins so it isnt immediately tried again by auto run reports
		IF @isSuccess = 1
			EXEC dbo.rpt_setScheduledReportNextRunDate @itemID=@itemID;
		ELSE
			UPDATE dbo.rpt_scheduledReports
			SET nextRunDate = dateadd(n,10,nextRunDate),
				dateLastUpdated = GETDATE()
			WHERE itemID = @itemID;
	COMMIT TRAN;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
